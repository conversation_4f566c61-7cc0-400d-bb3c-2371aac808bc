package com.bxm.customer.domain.query.valueAdded;

import com.bxm.common.core.web.domain.BaseVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 增值员工信息查询参数
 *
 * 说明：所有查询条件均为可选（除bizType外），最终条件由 Service 层进行动态拼接
 * 支持按业务类型查询：1-社保明细，2-个税明细
 * 支持多种查询条件的组合使用
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("增值员工信息查询参数")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmployeeQuery extends BaseVO {

    /** 业务类型（必填） */
    @ApiModelProperty(value = "业务类型：1-社保明细，2-个税明细", required = true, allowableValues = "1,2")
    private Integer bizType;

    /** 交付单编号（可选） */
    @ApiModelProperty(value = "交付单编号，支持精确匹配")
    private String deliveryOrderNo;

    /** 员工姓名（可选） */
    @ApiModelProperty(value = "员工姓名，支持模糊查询")
    private String employeeName;

    /** 身份证号（可选） */
    @ApiModelProperty(value = "身份证号，支持精确匹配")
    private String idNumber;

    /** 操作类型（可选） */
    @ApiModelProperty(value = "操作类型：1-提醒，2-更正，3-减员")
    private Integer operationType;

}
