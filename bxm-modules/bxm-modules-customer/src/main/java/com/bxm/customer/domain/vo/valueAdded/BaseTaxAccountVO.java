package com.bxm.customer.domain.vo.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 税务账号基础VO
 *
 * 包含个税账号和国税账号的公共字段，用于消除代码重复
 * 子类可以继承此基类并添加特有的业务逻辑和字段注释
 *
 * <AUTHOR>
 * @date 2025-08-19
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("税务账号基础VO")
public abstract class BaseTaxAccountVO {

    /** 主键ID */
    @ApiModelProperty(value = "账号ID")
    private Long id;

    /** 账号（税号） */
    @ApiModelProperty(value = "账号")
    private String accountNumber;

    /** 密码 */
    @ApiModelProperty(value = "密码")
    private String password;

    /** 登录方式 */
    @ApiModelProperty(value = "登录方式")
    private String loginMethod;

    /** 实名经办人 */
    @ApiModelProperty(value = "实名经办人")
    private String realNameAgent;

    /** 手机号 */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /** 身份证号 */
    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 操作方式 - 子类需要重写此字段的注释以体现具体业务含义 */
    @ApiModelProperty(value = "操作方式")
    private Integer operationType;
}
